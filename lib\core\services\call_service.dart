import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:phone_state/phone_state.dart';
import 'package:telephony/telephony.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:callway/core/config/config.dart';

class CallService extends GetxService {
  final Telephony telephony = Telephony.instance;
  StreamSubscription<PhoneState>? _phoneStateSubscription;
  String? _savedPhoneNumber;
  bool _isListening = false;
  final AppBuilder appBuilder = Get.find<AppBuilder>();

  @override
  void onInit() {
    super.onInit();
    _initializeService();
  }

  @override
  void onClose() {
    _phoneStateSubscription?.cancel();
    super.onClose();
  }

  Future<void> _initializeService() async {
    await _requestPermissions();
    await _loadSavedPhoneNumber();
    await _startListening();
  }

  Future<void> _requestPermissions() async {
    // طلب صلاحيات المكالمات والرسائل
    Map<Permission, PermissionStatus> permissions =
        await [
          Permission.phone,
          Permission.sms,
          Permission.notification,
        ].request();

    // التحقق من الصلاحيات
    if (permissions[Permission.phone] != PermissionStatus.granted) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'يجب السماح بصلاحية الوصول للمكالمات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    if (permissions[Permission.sms] != PermissionStatus.granted) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'يجب السماح بصلاحية إرسال الرسائل',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _loadSavedPhoneNumber() async {
    // تحميل الرقم المحفوظ باستخدام box.read("phone")
    try {
      _savedPhoneNumber = appBuilder.box.read("phone");
      debugPrint('تم تحميل الرقم المحفوظ: $_savedPhoneNumber');
    } catch (e) {
      debugPrint('خطأ في تحميل الرقم المحفوظ: $e');
    }
  }

  Future<void> _startListening() async {
    if (_isListening) return;

    try {
      _phoneStateSubscription = PhoneState.stream.listen(
        (PhoneState state) {
          _handlePhoneStateChange(state);
        },
        onError: (error) {
          debugPrint('خطأ في مراقبة حالة الهاتف: $error');
        },
      );
      _isListening = true;
      debugPrint('بدأت مراقبة المكالمات');
    } catch (e) {
      debugPrint('خطأ في بدء مراقبة المكالمات: $e');
    }
  }

  void _handlePhoneStateChange(PhoneState state) {
    debugPrint('تغيرت حالة الهاتف: ${state.status}');
    debugPrint('رقم المتصل: ${state.number}');

    // التحقق من وجود رقم محفوظ
    if (_savedPhoneNumber == null || _savedPhoneNumber!.isEmpty) {
      debugPrint('لا يوجد رقم محفوظ');
      return;
    }

    // التحقق من حالة المكالمة الواردة
    if (state.status == PhoneStateStatus.CALL_INCOMING) {
      _handleIncomingCall(state.number);
    }
  }

  void _handleIncomingCall(String? incomingNumber) {
    if (incomingNumber == null || incomingNumber.isEmpty) {
      debugPrint('رقم المتصل غير متوفر');
      return;
    }

    debugPrint('مكالمة واردة من: $incomingNumber');
    debugPrint('الرقم المحفوظ: $_savedPhoneNumber');

    // تنظيف الأرقام للمقارنة
    // String cleanIncomingNumber = _cleanPhoneNumber(incomingNumber);
    // String cleanSavedNumber = _cleanPhoneNumber(_savedPhoneNumber!);

    // مقارنة الأرقام
    if (_savedPhoneNumber == incomingNumber) {
      debugPrint('الرقم مطابق! سيتم إرسال الرسالة');
      _sendAutoReply(incomingNumber);
    } else {
      debugPrint('الرقم غير مطابق');
    }
  }

  // String _cleanPhoneNumber(String phoneNumber) {
  //   // إزالة المسافات والرموز الخاصة
  //   String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

  //   // إزالة رمز البلد إذا كان موجوداً
  //   if (cleaned.startsWith('+966')) {
  //     cleaned = cleaned.substring(4);
  //   } else if (cleaned.startsWith('966')) {
  //     cleaned = cleaned.substring(3);
  //   } else if (cleaned.startsWith('0')) {
  //     cleaned = cleaned.substring(1);
  //   }

  //   return cleaned;
  // }

  Future<void> _sendAutoReply(String phoneNumber) async {
    try {
      debugPrint('محاولة إرسال رسالة إلى: $phoneNumber');

      // إرسال الرسالة
      await telephony.sendSms(to: phoneNumber, message: 'صباح الخير');

      debugPrint('تم إرسال الرسالة بنجاح');

      // إظهار إشعار للمستخدم
      Get.snackbar(
        'تم إرسال الرسالة',
        'تم إرسال رسالة "صباح الخير" إلى $phoneNumber',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة: $e');

      Get.snackbar(
        'خطأ في إرسال الرسالة',
        'فشل في إرسال الرسالة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // تحديث الرقم المحفوظ
  void updateSavedPhoneNumber(String phoneNumber) {
    _savedPhoneNumber = phoneNumber;
    debugPrint('تم تحديث الرقم المحفوظ: $_savedPhoneNumber');
  }

  // إعادة تشغيل الخدمة
  Future<void> restartService() async {
    await _phoneStateSubscription?.cancel();
    _isListening = false;
    await _loadSavedPhoneNumber();
    await _startListening();
  }

  // التحقق من حالة الخدمة
  bool get isListening => _isListening;
  String? get savedPhoneNumber => _savedPhoneNumber;
}
