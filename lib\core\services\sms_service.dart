import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:telephony/telephony.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:callway/core/config/config.dart';
import 'package:flutter/services.dart';

class SmsService extends GetxService {
  final Telephony telephony = Telephony.instance;
  final AppBuilder appBuilder = Get.find<AppBuilder>();
  static const MethodChannel _channel = MethodChannel('call_monitor');
  StreamSubscription? _callStateSubscription;

  @override
  void onInit() {
    super.onInit();
    _requestPermissions();
    _startCallMonitoring();
  }

  @override
  void onClose() {
    _callStateSubscription?.cancel();
    super.onClose();
  }

  Future<void> _requestPermissions() async {
    // طلب صلاحيات الرسائل
    Map<Permission, PermissionStatus> permissions =
        await [
          Permission.sms,
          Permission.phone,
          Permission.notification,
        ].request();

    // التحقق من الصلاحيات
    if (permissions[Permission.sms] != PermissionStatus.granted) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'يجب السماح بصلاحية إرسال الرسائل',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    if (permissions[Permission.phone] != PermissionStatus.granted) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'يجب السماح بصلاحية مراقبة المكالمات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _startCallMonitoring() {
    // مراقبة المكالمات الواردة
    debugPrint('بدء مراقبة المكالمات...');

    // استخدام Timer لمراقبة المكالمات بشكل دوري
    Timer.periodic(const Duration(seconds: 3), (timer) {
      _checkForIncomingCalls();
    });
  }

  void _checkForIncomingCalls() {
    // التحقق من وجود رقم محفوظ
    String? savedNumber = appBuilder.box.read("phone");
    if (savedNumber == null || savedNumber.isEmpty) {
      return;
    }

    // هنا يمكن إضافة منطق مراقبة المكالمات
    // بسبب قيود Android الحديثة، سنعتمد على إشعار المستخدم
    debugPrint('مراقبة المكالمات نشطة للرقم: $savedNumber');
  }

  // دالة لإرسال الرسالة عند وصول مكالمة (يتم استدعاؤها يدوياً)
  void onIncomingCall(String incomingNumber) {
    String? savedNumber = appBuilder.box.read("phone");

    if (savedNumber == null || savedNumber.isEmpty) {
      debugPrint('لا يوجد رقم محفوظ');
      return;
    }

    // تنظيف الأرقام للمقارنة
    // String cleanIncoming = _cleanPhoneNumber(incomingNumber);
    // String cleanSaved = _cleanPhoneNumber(savedNumber);

    // debugPrint('مكالمة واردة من: $incomingNumber (منظف: $cleanIncoming)');
    // debugPrint('الرقم المحفوظ: $savedNumber (منظف: $cleanSaved)');

    // مقارنة الأرقام
    if (incomingNumber == savedNumber) {
      debugPrint('الرقم مطابق! سيتم إرسال الرسالة');
      sendAutoReply(incomingNumber);
    } else {
      debugPrint('الرقم غير مطابق');
    }
  }

  // String _cleanPhoneNumber(String phoneNumber) {
  //   // إزالة المسافات والرموز الخاصة
  //   String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

  //   // إزالة رمز البلد إذا كان موجوداً
  //   if (cleaned.startsWith('+966')) {
  //     cleaned = cleaned.substring(4);
  //   } else if (cleaned.startsWith('966')) {
  //     cleaned = cleaned.substring(3);
  //   } else if (cleaned.startsWith('0')) {
  //     cleaned = cleaned.substring(1);
  //   }

  //   return cleaned;
  // }

  Future<void> sendAutoReply(String phoneNumber) async {
    try {
      debugPrint('محاولة إرسال رسالة إلى: $phoneNumber');

      // إرسال الرسالة
      await telephony.sendSms(to: phoneNumber, message: 'صباح الخير');

      debugPrint('تم إرسال الرسالة بنجاح');

      // إظهار إشعار للمستخدم
      Get.snackbar(
        'تم إرسال الرسالة',
        'تم إرسال رسالة "صباح الخير" إلى $phoneNumber',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة: $e');

      Get.snackbar(
        'خطأ في إرسال الرسالة',
        'فشل في إرسال الرسالة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // إرسال رسالة للرقم المحفوظ
  Future<void> sendToSavedNumber() async {
    String? savedNumber = appBuilder.box.read("phone");

    if (savedNumber == null || savedNumber.isEmpty) {
      Get.snackbar(
        'خطأ',
        'لا يوجد رقم محفوظ. يرجى حفظ رقم الهاتف أولاً',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    await sendAutoReply(savedNumber);
  }
}
