import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:telephony/telephony.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:callway/core/config/config.dart';

class SmsService extends GetxService {
  final Telephony telephony = Telephony.instance;
  final AppBuilder appBuilder = Get.find<AppBuilder>();

  @override
  void onInit() {
    super.onInit();
    _requestPermissions();
  }

  Future<void> _requestPermissions() async {
    // طلب صلاحيات الرسائل
    Map<Permission, PermissionStatus> permissions = await [
      Permission.sms,
      Permission.notification,
    ].request();

    // التحقق من الصلاحيات
    if (permissions[Permission.sms] != PermissionStatus.granted) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'يجب السماح بصلاحية إرسال الرسائل',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> sendAutoReply(String phoneNumber) async {
    try {
      debugPrint('محاولة إرسال رسالة إلى: $phoneNumber');
      
      // إرسال الرسالة
      await telephony.sendSms(to: phoneNumber, message: 'صباح الخير');

      debugPrint('تم إرسال الرسالة بنجاح');

      // إظهار إشعار للمستخدم
      Get.snackbar(
        'تم إرسال الرسالة',
        'تم إرسال رسالة "صباح الخير" إلى $phoneNumber',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة: $e');

      Get.snackbar(
        'خطأ في إرسال الرسالة',
        'فشل في إرسال الرسالة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // إرسال رسالة للرقم المحفوظ
  Future<void> sendToSavedNumber() async {
    String? savedNumber = appBuilder.box.read("phone");
    
    if (savedNumber == null || savedNumber.isEmpty) {
      Get.snackbar(
        'خطأ',
        'لا يوجد رقم محفوظ. يرجى حفظ رقم الهاتف أولاً',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    await sendAutoReply(savedNumber);
  }
}
