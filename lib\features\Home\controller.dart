import 'package:callway/core/config/config.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomePageController extends GetxService {
  final controllerapp = Get.find<AppBuilder>();
  final phoneController = TextEditingController();
  late RxString phoneNumber = ''.obs;

  @override
  void onInit() {
    super.onInit();

    phoneNumber.value = controllerapp.box.read("phone") ?? '';
  }

  @override
  void onClose() {
    phoneController.dispose();
    super.onClose();
  }
}
