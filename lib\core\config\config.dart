import 'package:callway/core/config/role.dart';
import 'package:callway/core/routes/routes.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class AppBuilder extends GetxService {
  GetStorage box = GetStorage("app");

  late Role role;
  // GeneralUser? user;
  String? phone;

  loadData() async {
    await box.initStorage;

    if (!box.hasData("role")) {
      setRole(Role.unregistered);
    } else {
      role = Role.fromString(box.read("role"));
    }

    if (box.hasData("phone")) {
      phone = box.read("phone");
    }
  }

  setRole(Role role) {
    this.role = role;
    box.write("role", role.name);
  }

  setphone(String? phone) {
    this.phone = phone;
    if (phone != null) {
      box.write("phone", phone);
    } else {
      box.remove("phone");
    }
  }

  clearphone() {
    setRole(Role.unregistered);
    setphone(null);
  }

  init() async {
    await loadData();
    Get.offAllNamed(Pages.home.value);
  }
}
