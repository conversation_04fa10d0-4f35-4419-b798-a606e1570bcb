{"buildFiles": ["C:\\flutter_windows_3.29.0-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Flutter_Program\\Projects\\messegeFromCall\\android\\app\\.cxx\\Debug\\3x4y5p52\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Flutter_Program\\Projects\\messegeFromCall\\android\\app\\.cxx\\Debug\\3x4y5p52\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}