import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';

class LocationPickerController extends GetxService {
  GoogleMapController? mapController;
  Rx<LatLng> selectedLocation = LatLng(33.5138, 36.2765).obs;
  // المتغيرات التفاعلية
  late TextEditingController lang, lat;

  // متغيرات SMS
  @override
  onInit() {
    lang = TextEditingController();
    lat = TextEditingController();
    requestLocationPermission();

    super.onInit();
  }

  @override
  onClose() {
    lang.dispose();
    lat.dispose();
    super.onClose();
  }

  RxString selectedAddress = ''.obs;
  RxBool isLoading = false.obs;
  RxList<Marker> markers = <Marker>[].obs;

  // إنشاء الخريطة
  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
  }

  // الحصول على الموقع الحالي
  void getCurrentLocation() async {
    try {
      isLoading.value = true;

      // التحقق من الصلاحيات
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        Get.snackbar(
          "error",
          "location_service_disabled",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          Get.snackbar(
            "error",
            "location_permission_denied",

            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          isLoading.value = false;
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        Get.snackbar(
          "error",
          "location_permission_denied_forever",

          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return;
      }

      // الحصول على الموقع الحالي
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      LatLng currentLocation = LatLng(position.latitude, position.longitude);
    

    } catch (e) {
      Get.snackbar(
        "error",
        "location_error",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }


  // طلب صلاحيات الموقع
  void requestLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      await Geolocator.requestPermission();
    }
  }

  // طلب صلاحيات SMS
 
}
