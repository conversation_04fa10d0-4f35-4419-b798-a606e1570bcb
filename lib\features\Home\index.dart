import 'package:callway/core/config/config.dart';
import 'package:callway/features/Home/controller.dart';
import 'package:callway/core/services/sms_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(HomePageController());

    // تشغيل خدمة الرسائل
    Get.put(SmsService());

    return Scaffold(
      appBar: AppBar(title: const Center(child: Text("Call Way"))),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            // إشعار حالة الخدمة
            Container(
              margin: const EdgeInsets.all(16.0),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                border: Border.all(color: Colors.blue.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'خدمة الرسائل جاهزة. يمكنك إرسال رسالة "صباح الخير" للرقم المحفوظ بالضغط على زر الإرسال.',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Text(
                    'أدخل رقم الهاتف للرد التلقائي:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'سيتم إرسال رسالة "صباح الخير" تلقائياً عند وصول مكالمة من هذا الرقم',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: controller.phoneController,
                    keyboardType: TextInputType.phone,
                    textDirection: TextDirection.ltr,
                    decoration: InputDecoration(
                      hintText: '+966 50 123 4567',
                      prefixIcon: const Icon(Icons.phone_outlined),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          color: Colors.blue,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      if (controller.phoneController.text.isNotEmpty) {
                        controller.controllerapp.clearphone();
                        controller.phoneNumber.value =
                            controller.phoneController.text;
                        controller.controllerapp.setphone(
                          controller.phoneController.text,
                        );

                        // لا حاجة لتحديث خدمة إضافية

                        Get.snackbar(
                          'تم الحفظ',
                          'تم حفظ الرقم: ${controller.phoneController.text}\nيمكنك الآن إرسال رسالة "صباح الخير" بالضغط على زر الإرسال',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.green,
                          colorText: Colors.white,
                          duration: const Duration(seconds: 4),
                        );
                      } else {
                        Get.snackbar(
                          'خطأ',
                          'يرجى إدخال رقم الهاتف',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.red,
                          colorText: Colors.white,
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'حفظ الرقم',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // زر إرسال الرسالة
                  ElevatedButton.icon(
                    onPressed: () {
                      final smsService = Get.find<SmsService>();
                      smsService.sendToSavedNumber();
                    },
                    icon: const Icon(Icons.send),
                    label: const Text(
                      'إرسال رسالة "صباح الخير" للرقم المحفوظ',
                      style: TextStyle(fontSize: 16),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (controller.controllerapp.box.hasData("phone"))
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        border: Border.all(color: Colors.green.shade200),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.green.shade600,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Obx(
                              () => Text(
                                'الرقم المحفوظ: ${controller.phoneNumber}\n✅ يمكنك إرسال رسالة "صباح الخير" لهذا الرقم بالضغط على زر الإرسال',
                                style: TextStyle(
                                  color: Colors.green.shade700,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
