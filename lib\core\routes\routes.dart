// ignore_for_file: constant_identifier_names

import 'package:callway/features/Home/index.dart';
import 'package:callway/features/splash/index.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppRouting {
  static GetPage unknownRoute = GetPage(
    name: "/unknown",
    page: () => SizedBox(),
  );

  static GetPage initialRoute = GetPage(name: "/", page: () => SplashScreen());

  static List<GetPage> routes = [
    initialRoute,
    ...Pages.values.map((e) => e.page),
  ];
}

enum Pages {
  home
  //
  ;

  String get value => '/$name';

  GetPage get page => switch (this) {
    home => GetPage(name: value, page: () => HomePage()),
  };
}
